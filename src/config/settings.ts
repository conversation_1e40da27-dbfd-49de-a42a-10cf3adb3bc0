import { addApplicationForm } from './addApplication'
import { addDesktopSoftwareForm } from './addDesktopSoftware'
import { addServerSoftware } from './addServerSoftware'
import { checkApplicationForm } from './applicationCheck'
import { addAPI } from './information-flow/api'
import { addKafkaConsumer } from './information-flow/kafkaConsumer'
import { addKafkaInterface } from './information-flow/kafkaInterface'
import { addMFT } from './information-flow/mft'
import { transferApplicationForm } from './transferApplication'
import { eutForm } from "./eut"
import { uc5Form } from './uc5'

export const WIZARDS = [
  checkApplicationForm,
  transferApplicationForm,
  addApplicationForm,
  addDesktopSoftwareForm,
  addServerSoftware,
  addMFT,
  addKafkaConsumer,
  addKafkaInterface,
  addAPI,
  eutForm,
  uc5Form
]
