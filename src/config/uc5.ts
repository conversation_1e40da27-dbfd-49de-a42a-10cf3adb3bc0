import type { FactSheetData, ReportUser, WizardFormModel } from "../model";
import dayjs from 'dayjs';
import { CREATE_FACTSHEET_MUTATION } from '../graphql';

declare const lx: any;

// Constants
const COMPANIES = {
  HELVETIA_AG: 'Helvetia Schweizerische Versicherungsgesellschaft AG',
  HELVETIA_ES: 'HELVETIA COMPAÑIA SUIZA SOCIEDAD ANONIMA DE SEGUROS Y REASEGUROS'
} as const;

const TAGS = {
  EXPOSED: 'exposed',
  FQDN: 'fqdn',
  EV_TRUE: 'ev-true',
  EV_FALSE: 'ev-false',
  EXPOSED_FQDN: 'ExposedFQDN'
} as const;

// Fixed tag IDs
const FIXED_TAG_IDS = {
  EXPOSED_FQDN: '69e9b0ca-8b41-475d-b8d2-0a715184e555'
} as const;

const MESSAGES = {
  SAVE_ERROR: 'Error while creating interface. Please try again!',
  SAVE_SUCCESS: 'Interface successfully created',
  INTERFACE_EXISTS: 'An interface with the name "{name}" already exists. Please choose a different name.',
  START_DATE_WARNING: 'When should this interface go live? Note: SNOW sync takes at least 8 hours (3x per day)',
  EV_DESCRIPTION: 'Choose the EV setting for this interface (Exposed and FQDN tags will be set automatically)',
  COMPANY_DESCRIPTION: 'Company for certificate issuance (automatically set based on application prefix)',
  REMARKS_DESCRIPTION: 'Purpose/usage description (e.g., "Just for testing for the Corporate PKI Team")',
  REMARKS_PLACEHOLDER: 'Just for testing for the Corporate PKI Team'
} as const;

const UC5_CONFIG = {
  FORM_ID: 'uc5',
  FORM_NAME: 'UC5 - New Interface',
  ORIGIN_FACTSHEET: 'Interface',
  STEP_NAME: 'Interface Information',
  UC_VALUE: 'External TLS Server Certificate',
  ES_PREFIX: 'ES-'
} as const;

// Function to get all tags and find the ones we need
const getUC5Tags = async () => {
  const query = `{
    allTags{
      edges{
        node{
          id
          name
          tagGroup {
            id
            description
          }
        }
      }
    }
  }`;

  const data = await lx.executeGraphQL(query);
  const tags = data.allTags.edges.map((edge: any) => edge.node);

  // Find the specific tags we need
  const exposedTag = tags.find((tag: any) => tag.name.toLowerCase() === TAGS.EXPOSED);
  const fqdnTag = tags.find((tag: any) => tag.name.toLowerCase() === TAGS.FQDN);
  const evTrueTag = tags.find((tag: any) => tag.name.toLowerCase() === TAGS.EV_TRUE);
  const evFalseTag = tags.find((tag: any) => tag.name.toLowerCase() === TAGS.EV_FALSE);
  const exposedFqdnTag = tags.find((tag: any) => tag.name === TAGS.EXPOSED_FQDN);

  return {
    exposed: exposedTag?.id,
    fqdn: fqdnTag?.id,
    evTrue: evTrueTag?.id,
    evFalse: evFalseTag?.id,
    exposedFqdn: exposedFqdnTag?.id || FIXED_TAG_IDS.EXPOSED_FQDN // Use fixed ID if not found
  };
};

export const uc5Form: WizardFormModel = {
  id: UC5_CONFIG.FORM_ID,
  name: UC5_CONFIG.FORM_NAME,
  originFactSheet: UC5_CONFIG.ORIGIN_FACTSHEET,
  addWithTemporaryMode: false,
  onlyAdmin: false,
  saveErrorMessage: MESSAGES.SAVE_ERROR,
  saveSuccessMessage: MESSAGES.SAVE_SUCCESS,
  steps: [
    {
      name: UC5_CONFIG.STEP_NAME,
      rules: [
        // Rule to set company based on selected application prefix
        async (form, steps, currentStep, currentUser, update) => {
          const selectedAppId = form.getFieldValue('selectedApplication');
          if (selectedAppId) {
            // Get application details to check the name/prefix
            const appQuery = `{
              factSheet(id: "${selectedAppId}") {
                ... on Application {
                  id
                  name
                  displayName
                }
              }
            }`;

            try {
              const appData = await lx.executeGraphQL(appQuery);
              const appName = appData.factSheet.name || appData.factSheet.displayName;

              // Check if application starts with ES-
              if (appName.startsWith(UC5_CONFIG.ES_PREFIX)) {
                form.setFieldValue('company', COMPANIES.HELVETIA_ES);
              } else {
                // For all other prefixes (GF-, AT-, CH-, IT-, FR-, etc.)
                form.setFieldValue('company', COMPANIES.HELVETIA_AG);
              }
            } catch (error) {
              console.error('Error fetching application details:', error);
            }
          }
        }
      ],
      fields: [
        {
          title: 'Interface Name',
          name: 'interfaceName',
          fieldType: 'Text',
          description: 'Name for the new interface',
          required: true,
          visible: true
        },
        {
          title: 'Application',
          name: 'selectedApplication',
          fieldType: 'ApplicationSelect',
          loadFactSheet: 'Application',
          description: 'Select the application for which you want to create an interface',
          required: true,
          visible: true
        },
        {
          title: 'Start Date',
          name: 'startDate',
          fieldType: 'DatePicker',
          description: MESSAGES.START_DATE_WARNING,
          required: true,
          visible: true
        },
        {
          title: 'EV Setting',
          name: 'evSetting',
          fieldType: 'SingleSelect',
          description: MESSAGES.EV_DESCRIPTION,
          required: true,
          visible: true,
          options: [
            { label: 'EV-true', value: 'EV-true' },
            { label: 'EV-false', value: 'EV-false' }
          ]
        },
        {
          title: 'Company',
          name: 'company',
          fieldType: 'SingleSelect',
          description: MESSAGES.COMPANY_DESCRIPTION,
          required: true,
          visible: true,
          disabled: true,
          options: [
            { label: COMPANIES.HELVETIA_AG, value: COMPANIES.HELVETIA_AG },
            { label: COMPANIES.HELVETIA_ES, value: COMPANIES.HELVETIA_ES }
          ]
        },
        {
          title: 'Remarks',
          name: 'remarks',
          fieldType: 'TextArea',
          description: MESSAGES.REMARKS_DESCRIPTION,
          required: true,
          visible: true
        }
      ],
      customChecks: async (form, currentUser, currentStep) => {
        const interfaceName = form.getFieldValue('interfaceName');

        if (interfaceName && interfaceName.trim().length > 0) {
          try {
            // Check if interface with this name already exists
            const searchName = interfaceName.trim().replace(/"/g, '\\"'); // Escape quotes
            const checkQuery = `{
              allFactSheets(filter: {
                facetFilters: [{
                  facetKey: "FactSheetTypes",
                  keys: ["Interface"]
                }],
                fullTextSearch: "${searchName}"
              }) {
                edges {
                  node {
                    id
                    name
                    displayName
                  }
                }
              }
            }`;

            const result = await lx.executeGraphQL(checkQuery);
            const existingInterfaces = result.allFactSheets.edges;

            // Check for exact name match
            const exactMatch = existingInterfaces.find((edge: any) =>
              edge.node.name === interfaceName.trim() ||
              edge.node.displayName === interfaceName.trim()
            );

            if (exactMatch) {
              return MESSAGES.INTERFACE_EXISTS.replace('{name}', interfaceName);
            }
          } catch (error) {
            console.error('Error checking interface name:', error);
            // Don't block the form if the check fails, just log the error
          }
        }

        return undefined;
      }
    }
  ],
  init: (form) => {
    // Set default values
    form.setFieldValue('company', COMPANIES.HELVETIA_AG);
  },
  save: async (data: FactSheetData, currentUser: ReportUser, docId: string | undefined): Promise<string | undefined> => {
    console.log('UC5 - DATA:', data);

    try {
      // Use the user-provided interface name
      const interfaceName = data.interfaceName;

      const variables: Record<string, any> = {
        input: {
          name: interfaceName,
          type: 'Interface'
        },
        patches: []
      };

      // Add description with UC5 details in the exact required format
      const description = `UC="${UC5_CONFIG.UC_VALUE}";OrgEntry="${data.company}";Remarks="${data.remarks}"`;

      variables.patches.push({
        op: 'replace',
        path: '/description',
        value: description
      });

      // Set lifecycle with start date
      if (data.startDate) {
        variables.patches.push({
          op: 'replace',
          path: '/lifecycle',
          value: `{"phases":[{"phase":"active","startDate":"${dayjs(data.startDate).format('YYYY-MM-DD')}"}]}`
        });
      }

      // Link to the selected application as provider
      if (data.selectedApplication) {
        variables.patches.push({
          op: 'add',
          path: `/relInterfaceToProviderApplication/new_provider_${data.selectedApplication}`,
          value: `{"factSheetId":"${data.selectedApplication}"}`
        });
      }

      // Get UC5 specific tags
      const uc5Tags = await getUC5Tags();
      const tags = [];

      // Always add "Exposed" tag
      if (uc5Tags.exposed) {
        tags.push(uc5Tags.exposed);
      } else {
        console.warn('UC5: Exposed tag not found in LeanIX');
      }

      // Always add "FQDN" tag
      if (uc5Tags.fqdn) {
        tags.push(uc5Tags.fqdn);
      } else {
        console.warn('UC5: FQDN tag not found in LeanIX');
      }

      // Always add "ExposedFQDN" tag
      if (uc5Tags.exposedFqdn) {
        tags.push(uc5Tags.exposedFqdn);
      } else {
        console.warn('UC5: ExposedFQDN tag not found in LeanIX');
      }

      // Add EV tag based on user selection
      if (data.evSetting === 'EV-true' && uc5Tags.evTrue) {
        tags.push(uc5Tags.evTrue);
      } else if (data.evSetting === 'EV-false' && uc5Tags.evFalse) {
        tags.push(uc5Tags.evFalse);
      } else {
        console.warn(`UC5: ${data.evSetting} tag not found in LeanIX`);
      }

      // Add tags to patches
      tags.forEach(tagId => {
        variables.patches.push({
          op: 'add',
          path: '/tags',
          value: `[{"tagId":"${tagId}"}]`
        });
      });

      return lx.executeGraphQL(CREATE_FACTSHEET_MUTATION, JSON.stringify(variables)).then((result) => {
        return result?.createFactSheet?.factSheet?.id;
      }).catch((error: any) => {
        console.error(error);
        lx.showToastr('error', MESSAGES.SAVE_ERROR);
        return undefined;
      });

    } catch (error: any) {
      console.error('UC5 Save Error:', error);
      lx.showToastr('error', MESSAGES.SAVE_ERROR);
      return undefined;
    }
  }
};